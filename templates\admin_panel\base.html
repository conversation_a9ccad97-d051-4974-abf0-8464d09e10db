<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Dashboard{% endblock %} - Pentora Admin</title>

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/summernote-bs5.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --sidebar-width: 280px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #f8fafc;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }

        .top-navbar {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem 2rem;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .content-area {
            padding: 2rem;
        }

        /* Summernote Bootstrap 5 Compatibility Fixes */
        .note-editor {
            position: relative;
            z-index: 1;
        }

        .note-editor .note-toolbar {
            position: relative;
            z-index: 10;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .note-editor .dropdown-menu {
            position: absolute !important;
            z-index: 9999 !important;
            display: none;
            min-width: 160px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 6px 12px rgba(0,0,0,.175);
            top: 100% !important;
            left: 0 !important;
            transform: none !important;
        }

        .note-editor .dropdown-menu.show {
            display: block !important;
        }

        .note-editor .note-color .dropdown-menu {
            min-width: 290px !important;
            padding: 5px;
        }

        .note-editor .note-fontsize .dropdown-menu {
            min-width: 90px;
        }

        .note-editor .note-style .dropdown-menu {
            min-width: 200px;
        }

        .note-editor .note-color-palette {
            line-height: 1;
        }

        .note-editor .note-color-palette .note-color-btn {
            width: 20px;
            height: 20px;
            padding: 0;
            margin: 1px;
            border: 1px solid #fff;
            cursor: pointer;
        }

        .note-editor .note-color-palette .note-color-btn:hover {
            border-color: #000;
        }

        /* Fix dropdown positioning issues */
        .note-editor .note-btn-group {
            position: relative;
        }

        .note-editor .note-btn-group .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: auto;
            bottom: auto;
        }

        /* Table styling in editor */
        .note-editor .note-editable table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }

        .note-editor .note-editable table td,
        .note-editor .note-editable table th {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .note-editor .note-editable table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        /* Video styling in editor */
        .note-editor .note-editable iframe,
        .note-editor .note-editable video {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
        }

        .note-editor .note-editable .video-container {
            position: relative;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            height: 0;
            overflow: hidden;
            margin: 10px 0;
        }

        .note-editor .note-editable .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .card {
            border: none;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 0.75rem;
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1.5rem;
            font-weight: 600;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
        }

        .stats-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
            color: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .stats-card.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #10b981 100%);
        }

        .stats-card.warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
        }

        .stats-card.danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #ef4444 100%);
        }

        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .table thead th {
            background-color: #f8fafc;
            border: none;
            font-weight: 600;
            color: #374151;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        .modal-content {
            border-radius: 1rem;
            border: none;
        }

        .modal-header {
            border-bottom: 1px solid #e2e8f0;
            padding: 1.5rem;
        }

        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid #d1d5db;
            padding: 0.75rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-area {
                padding: 1rem;
            }
        }

        /* Loading spinner */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4 class="mb-0">
                <i class="fas fa-graduation-cap me-2"></i>
                Pentora Admin
            </h4>
            <small class="text-muted">Learning Platform</small>
        </div>

        <div class="sidebar-nav">
            <div class="nav-item">
                <a href="{% url 'admin_panel:dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'admin_panel:subjects' %}" class="nav-link {% if 'subjects' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-book"></i>
                    Subjects
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'admin_panel:levels' %}" class="nav-link {% if 'levels' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-layer-group"></i>
                    Class Levels
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'core:feedback_list' %}" class="nav-link {% if 'feedback' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-comments"></i>
                    User Feedback
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'admin_panel:topics' %}" class="nav-link {% if 'topics' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-list"></i>
                    Topics
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'admin_panel:manage_study_notes' %}" class="nav-link {% if 'study_note' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-book-open"></i>
                    Study Notes
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'admin_panel:questions' %}" class="nav-link {% if 'questions' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-question-circle"></i>
                    Questions
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'admin_panel:csv_import' %}" class="nav-link {% if 'import' in request.resolver_match.url_name and 'logs' not in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-file-csv"></i>
                    CSV Import
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'admin_panel:users' %}" class="nav-link {% if 'users' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-users"></i>
                    Users
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'admin_panel:reports' %}" class="nav-link {% if 'reports' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-chart-bar"></i>
                    Reports
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'analytics:dashboard' %}" class="nav-link {% if 'analytics' in request.resolver_match.url_name %}active{% endif %}" onclick="toggleAnalyticsSubmenu()">
                    <i class="fas fa-chart-line"></i>
                    Analytics & Monitoring
                    <i class="fas fa-chevron-down ms-auto" id="analytics-chevron" style="font-size: 0.8em;"></i>
                </a>
                <!-- Performance Tracking Sub-menu -->
                <div class="ms-4 mt-2" id="analytics-submenu" style="{% if 'analytics' not in request.resolver_match.url_name %}display: none;{% endif %}">
                    <a href="{% url 'analytics:dashboard' %}#performance-tracking" class="nav-link small" style="color: rgba(255, 255, 255, 0.7); padding: 0.5rem 1rem;">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Performance Tools
                    </a>
                    <a href="{% url 'analytics:dashboard' %}#system-health" class="nav-link small" style="color: rgba(255, 255, 255, 0.7); padding: 0.5rem 1rem;">
                        <i class="fas fa-heartbeat me-2"></i>
                        System Health
                    </a>
                    <a href="{% url 'analytics:dashboard' %}#user-analytics" class="nav-link small" style="color: rgba(255, 255, 255, 0.7); padding: 0.5rem 1rem;">
                        <i class="fas fa-users me-2"></i>
                        User Analytics
                    </a>
                </div>
            </div>

            <div class="nav-item">
                <a href="/admin/billing/" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    Billing Management
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'admin_panel:settings' %}" class="nav-link {% if 'settings' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-cog"></i>
                    Site Settings
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'admin_panel:import_logs' %}" class="nav-link {% if 'logs' in request.resolver_match.url_name %}active{% endif %}">
                    <i class="fas fa-list-alt"></i>
                    Logs
                </a>
            </div>

            <div class="nav-item mt-4">
                <a href="{% url 'core:home' %}" class="nav-link">
                    <i class="fas fa-home"></i>
                    Back to Site
                </a>
            </div>

            <div class="nav-item">
                <a href="{% url 'users:logout' %}" class="nav-link text-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div class="d-flex align-items-center">
                <button class="btn btn-link d-md-none me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0">{% block page_title %}Dashboard{% endblock %}</h5>
            </div>

            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2"></i>
                        {{ user.full_name }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{% url 'users:profile' %}">Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="{% url 'users:logout' %}">Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/summernote-bs5.min.js"></script>

    <script>
        // Sidebar toggle for mobile
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('show');
        });

        // Initialize rich text editor with comprehensive formatting
        $(document).ready(function() {
            console.log('Document ready, checking for Summernote...');
            console.log('jQuery version:', $.fn.jquery);
            console.log('Summernote available:', typeof $.fn.summernote !== 'undefined');

            // Check if summernote elements exist
            console.log('Found .summernote elements:', $('.summernote').length);

            // Full toolbar for study notes content
            $('.summernote').not('.summernote-compact').summernote({
                height: 300,
                focus: false,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['color', ['forecolor', 'backcolor']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video', 'hr']],
                    ['view', ['fullscreen', 'codeview']]
                ],
                fontSizes: ['8', '10', '12', '14', '16', '18', '20', '24', '28', '32', '36', '48'],
                styleTags: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
                dialogsInBody: true,
                placeholder: 'Start writing your content here...',
                popover: {
                    video: [
                        ['video', ['videoAttributes', 'videoShape', 'videoSize', 'videoFloat', 'removeVideo']]
                    ]
                },
                videoAttributes: {
                    removeEmpty: false
                },
                callbacks: {
                    onInit: function() {
                        console.log('Summernote initialized successfully');

                        // Fix dropdown functionality after initialization
                        setTimeout(function() {
                            $('.note-editor .dropdown-toggle').off('click.bs.dropdown');
                            $('.note-editor .dropdown-toggle').on('click', function(e) {
                                e.preventDefault();
                                e.stopPropagation();

                                var $dropdown = $(this).next('.dropdown-menu');
                                var isVisible = $dropdown.hasClass('show');

                                // Hide all dropdowns first
                                $('.note-editor .dropdown-menu').removeClass('show');

                                // Show current dropdown if it wasn't visible
                                if (!isVisible) {
                                    $dropdown.addClass('show');
                                }
                            });

                            // Auto-close dropdown when option is selected
                            $('.note-editor .dropdown-menu').on('click', 'a, button, .note-color-btn', function(e) {
                                // Close the dropdown after selection
                                setTimeout(function() {
                                    $('.note-editor .dropdown-menu').removeClass('show');
                                }, 100);
                            });
                        }, 100);
                    }
                }
            });

            // Compact toolbar for question text (shorter content)
            $('.summernote-compact').summernote({
                height: 150,
                minHeight: 100,
                maxHeight: 300,
                focus: false,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['color', ['forecolor']],
                    ['para', ['ul', 'ol']],
                    ['insert', ['link']],
                    ['view', ['fullscreen', 'codeview']]
                ],
                fontSizes: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '22', '24', '26', '28', '30'],
                styleTags: ['p', 'h4', 'h5', 'h6'],
                placeholder: 'Enter your question here...',
                dialogsInBody: true,
                callbacks: {
                    onInit: function() {
                        console.log('Summernote compact initialized');
                    }
                }
            });

            // Enhanced global click handler to close Summernote dropdowns
            $(document).on('click', function(e) {
                // Close dropdowns when clicking outside the editor or on non-dropdown elements
                if (!$(e.target).closest('.note-editor .dropdown').length) {
                    $('.note-editor .dropdown-menu').removeClass('show');
                }
            });

            // Close dropdown when clicking on the editor content area
            $(document).on('click', '.note-editable', function(e) {
                $('.note-editor .dropdown-menu').removeClass('show');
            });


        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);

        // Analytics submenu toggle function
        function toggleAnalyticsSubmenu() {
            const submenu = document.getElementById('analytics-submenu');
            const chevron = document.getElementById('analytics-chevron');

            if (submenu.style.display === 'none' || submenu.style.display === '') {
                submenu.style.display = 'block';
                chevron.classList.remove('fa-chevron-down');
                chevron.classList.add('fa-chevron-up');
            } else {
                submenu.style.display = 'none';
                chevron.classList.remove('fa-chevron-up');
                chevron.classList.add('fa-chevron-down');
            }
        }

        // Show submenu if on analytics page
        document.addEventListener('DOMContentLoaded', function() {
            if (window.location.pathname.includes('/analytics/')) {
                const submenu = document.getElementById('analytics-submenu');
                const chevron = document.getElementById('analytics-chevron');
                if (submenu) {
                    submenu.style.display = 'block';
                    chevron.classList.remove('fa-chevron-down');
                    chevron.classList.add('fa-chevron-up');
                }
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
